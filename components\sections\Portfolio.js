'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Link from 'next/link'
import { ArrowRight, ExternalLink, Github } from 'lucide-react'
import Button from '../ui/Button'

const Portfolio = () => {
  const [activeCategory, setActiveCategory] = useState('All')

  const categories = ['All', 'Web Design', 'Mobile App', 'Web App', 'Software Solution']

  const portfolioItems = [
    {
      id: 1,
      title: 'E-commerce Fashion Platform',
      category: 'Web Design',
      description: 'A complete e-commerce redesign that increased conversion rates by 180%',
      image: 'https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=600&h=400&fit=crop',
      technologies: ['React', 'Next.js', 'Stripe', 'Tailwind CSS'],
      results: '180% increase in conversion rate',
      link: '#'
    },
    {
      id: 2,
      title: 'Healthcare Mobile App',
      category: 'Mobile App',
      description: 'Telemedicine app connecting patients with doctors seamlessly',
      image: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=600&h=400&fit=crop',
      technologies: ['React Native', 'Node.js', 'MongoDB', 'WebRTC'],
      results: '100K+ downloads in first month',
      link: '#'
    },
    {
      id: 3,
      title: 'Project Management Platform',
      category: 'Web App',
      description: 'Advanced project management with real-time collaboration',
      image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=600&h=400&fit=crop',
      technologies: ['Vue.js', 'Laravel', 'MySQL', 'WebSocket'],
      results: '50% improvement in team productivity',
      link: '#'
    },
    {
      id: 4,
      title: 'Financial Dashboard',
      category: 'Software Solution',
      description: 'Comprehensive financial dashboard with real-time market data',
      image: 'https://images.unsplash.com/photo-**********-bebda4e38f71?w=600&h=400&fit=crop',
      technologies: ['Angular', 'Python', 'Django', 'D3.js'],
      results: '60% faster decision making',
      link: '#'
    },
    {
      id: 5,
      title: 'Food Delivery App',
      category: 'Mobile App',
      description: 'Feature-rich food delivery app with real-time tracking',
      image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=600&h=400&fit=crop',
      technologies: ['Flutter', 'Firebase', 'Google Maps', 'Stripe'],
      results: '300% increase in online orders',
      link: '#'
    },
    {
      id: 6,
      title: 'Learning Management System',
      category: 'Web App',
      description: 'Comprehensive LMS with interactive courses and assessments',
      image: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600&h=400&fit=crop',
      technologies: ['React', 'Node.js', 'MongoDB', 'WebRTC'],
      results: '50,000+ active students',
      link: '#'
    }
  ]

  const filteredItems = activeCategory === 'All' 
    ? portfolioItems 
    : portfolioItems.filter(item => item.category === activeCategory)

  return (
    <section className="section-padding bg-secondary-50">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mb-4">
            Our Portfolio
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-heading font-bold text-secondary-900 mb-6">
            Showcasing Our Best Work
          </h2>
          <p className="text-xl text-secondary-600 max-w-3xl mx-auto leading-relaxed">
            Explore our portfolio of successful projects that have transformed businesses 
            and delivered exceptional results for our clients.
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setActiveCategory(category)}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                activeCategory === category
                  ? 'bg-primary-600 text-white shadow-lg'
                  : 'bg-white text-secondary-700 hover:bg-primary-50 hover:text-primary-600'
              }`}
            >
              {category}
            </button>
          ))}
        </motion.div>

        {/* Portfolio Grid */}
        <motion.div
          layout
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          <AnimatePresence>
            {filteredItems.map((item, index) => (
              <motion.div
                key={item.id}
                layout
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="group bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500"
              >
                {/* Image */}
                <div className="relative overflow-hidden">
                  <img
                    src={item.image}
                    alt={item.title}
                    className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="absolute bottom-4 left-4 right-4 flex gap-2">
                      <button className="p-2 bg-white/20 backdrop-blur-sm rounded-lg text-white hover:bg-white/30 transition-colors">
                        <ExternalLink className="w-4 h-4" />
                      </button>
                      <button className="p-2 bg-white/20 backdrop-blur-sm rounded-lg text-white hover:bg-white/30 transition-colors">
                        <Github className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className="px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm font-medium">
                      {item.category}
                    </span>
                  </div>
                  
                  <h3 className="text-xl font-heading font-bold text-secondary-900 mb-3 group-hover:text-primary-600 transition-colors">
                    {item.title}
                  </h3>
                  
                  <p className="text-secondary-600 mb-4 leading-relaxed">
                    {item.description}
                  </p>

                  {/* Technologies */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {item.technologies.slice(0, 3).map((tech) => (
                      <span
                        key={tech}
                        className="px-2 py-1 bg-secondary-100 text-secondary-700 rounded text-xs"
                      >
                        {tech}
                      </span>
                    ))}
                    {item.technologies.length > 3 && (
                      <span className="px-2 py-1 bg-secondary-100 text-secondary-700 rounded text-xs">
                        +{item.technologies.length - 3} more
                      </span>
                    )}
                  </div>

                  {/* Results */}
                  <div className="mb-4">
                    <p className="text-sm font-medium text-green-600">
                      {item.results}
                    </p>
                  </div>

                  {/* CTA */}
                  <Link href={item.link}>
                    <Button 
                      variant="outline" 
                      size="sm"
                      className="w-full group-hover:bg-primary-600 group-hover:text-white group-hover:border-primary-600"
                      icon={<ArrowRight className="w-4 h-4" />}
                      iconPosition="right"
                    >
                      View Details
                    </Button>
                  </Link>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <Link href="/portfolio">
            <Button variant="primary" size="lg">
              View All Projects
            </Button>
          </Link>
        </motion.div>
      </div>
    </section>
  )
}

export default Portfolio
