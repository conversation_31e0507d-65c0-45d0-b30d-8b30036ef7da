'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Star, Quote, ChevronLeft, ChevronRight } from 'lucide-react'

const Testimonials = () => {
  const [currentIndex, setCurrentIndex] = useState(0)

  const testimonials = [
    {
      id: 1,
      name: '<PERSON>',
      position: 'CEO',
      company: 'TechStart Inc.',
      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
      content: 'Working with this team was an absolute game-changer for our business. They delivered a stunning website that not only looks incredible but has increased our conversion rate by 180%. Their attention to detail and commitment to excellence is unmatched.',
      rating: 5,
      project: 'E-commerce Website Redesign',
      result: '180% increase in conversion rate'
    },
    {
      id: 2,
      name: '<PERSON>',
      position: 'Founder',
      company: 'HealthTech Solutions',
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      content: 'The mobile app they developed for us exceeded all expectations. The user experience is seamless, and we\'ve seen incredible user engagement. They truly understand how to build products that users love.',
      rating: 5,
      project: 'Healthcare Mobile App',
      result: '95% user satisfaction rate'
    },
    {
      id: 3,
      name: 'Emily Rodriguez',
      position: 'Marketing Director',
      company: 'Global Retail Co.',
      image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
      content: 'Their web application transformed how we manage our inventory and customer relationships. The ROI was evident within the first month of implementation. Highly professional team with exceptional technical skills.',
      rating: 5,
      project: 'Custom Web Application',
      result: '300% ROI in first month'
    },
    {
      id: 4,
      name: 'David Thompson',
      position: 'CTO',
      company: 'FinanceFlow',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      content: 'They helped us modernize our legacy systems and integrate everything seamlessly. The new software solution has improved our operational efficiency by 60%. Outstanding work and great communication throughout.',
      rating: 5,
      project: 'Legacy System Modernization',
      result: '60% efficiency improvement'
    },
    {
      id: 5,
      name: 'Lisa Park',
      position: 'Product Manager',
      company: 'EduTech Platform',
      image: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=100&h=100&fit=crop&crop=face',
      content: 'The team delivered exactly what we envisioned and more. Their expertise in both design and development is remarkable. Our platform now serves over 50,000 students with zero downtime.',
      rating: 5,
      project: 'Educational Platform Development',
      result: '50,000+ active users'
    }
  ]

  const stats = [
    { number: '150+', label: 'Projects Completed' },
    { number: '98%', label: 'Client Satisfaction' },
    { number: '50+', label: 'Happy Clients' },
    { number: '24/7', label: 'Support Available' }
  ]

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
      )
    }, 5000)

    return () => clearInterval(timer)
  }, [testimonials.length])

  const nextTestimonial = () => {
    setCurrentIndex(currentIndex === testimonials.length - 1 ? 0 : currentIndex + 1)
  }

  const prevTestimonial = () => {
    setCurrentIndex(currentIndex === 0 ? testimonials.length - 1 : currentIndex - 1)
  }

  const currentTestimonial = testimonials[currentIndex]

  return (
    <section className="section-padding bg-white">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mb-4">
            Client Testimonials
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-heading font-bold text-secondary-900 mb-6">
            What Our Clients Say
          </h2>
          <p className="text-xl text-secondary-600 max-w-3xl mx-auto leading-relaxed">
            Don't just take our word for it. Here's what our satisfied clients have to say 
            about working with us and the results we've delivered.
          </p>
        </motion.div>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16"
        >
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-primary-600 mb-2">
                {stat.number}
              </div>
              <div className="text-secondary-600 font-medium">
                {stat.label}
              </div>
            </div>
          ))}
        </motion.div>

        {/* Testimonial Carousel */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="relative max-w-4xl mx-auto"
        >
          <div className="bg-gradient-to-br from-primary-50 to-accent-50 rounded-3xl p-8 md:p-12 relative overflow-hidden">
            {/* Background Quote */}
            <Quote className="absolute top-8 right-8 w-16 h-16 text-primary-200 opacity-50" />
            
            <AnimatePresence mode="wait">
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.5 }}
                className="relative z-10"
              >
                {/* Rating */}
                <div className="flex items-center justify-center mb-6">
                  {[...Array(currentTestimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
                  ))}
                </div>

                {/* Content */}
                <blockquote className="text-xl md:text-2xl text-secondary-800 text-center mb-8 leading-relaxed font-medium">
                  "{currentTestimonial.content}"
                </blockquote>

                {/* Author */}
                <div className="flex items-center justify-center space-x-4 mb-6">
                  <img
                    src={currentTestimonial.image}
                    alt={currentTestimonial.name}
                    className="w-16 h-16 rounded-full object-cover"
                  />
                  <div className="text-center">
                    <div className="font-bold text-secondary-900 text-lg">
                      {currentTestimonial.name}
                    </div>
                    <div className="text-secondary-600">
                      {currentTestimonial.position}, {currentTestimonial.company}
                    </div>
                  </div>
                </div>

                {/* Project Info */}
                <div className="text-center">
                  <div className="inline-flex items-center px-4 py-2 bg-white rounded-full text-sm">
                    <span className="text-secondary-600 mr-2">Project:</span>
                    <span className="font-medium text-secondary-900">{currentTestimonial.project}</span>
                  </div>
                  <div className="mt-2">
                    <span className="text-green-600 font-semibold">{currentTestimonial.result}</span>
                  </div>
                </div>
              </motion.div>
            </AnimatePresence>

            {/* Navigation */}
            <div className="flex items-center justify-between mt-8">
              <button
                onClick={prevTestimonial}
                className="p-3 bg-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:bg-primary-50"
              >
                <ChevronLeft className="w-6 h-6 text-secondary-700" />
              </button>

              {/* Dots */}
              <div className="flex space-x-2">
                {testimonials.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentIndex(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      index === currentIndex ? 'bg-primary-600' : 'bg-secondary-300'
                    }`}
                  />
                ))}
              </div>

              <button
                onClick={nextTestimonial}
                className="p-3 bg-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:bg-primary-50"
              >
                <ChevronRight className="w-6 h-6 text-secondary-700" />
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Testimonials
