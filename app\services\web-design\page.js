import Header from '../../../components/layout/Header'
import Footer from '../../../components/layout/Footer'
import { Palette, CheckCircle, ArrowRight } from 'lucide-react'
import Button from '../../../components/ui/Button'

export const metadata = {
  title: 'Web Design Services | Premium Freelance Agency',
  description: 'Professional web design services that convert visitors into customers. Custom UI/UX design, responsive layouts, and conversion optimization.',
}

export default function WebDesignPage() {
  const features = [
    'Custom UI/UX Design',
    'Responsive Design',
    'Brand Identity Integration',
    'Conversion Optimization',
    'Accessibility Compliance',
    'Performance Optimization',
    'SEO-Friendly Design',
    'Cross-Browser Compatibility'
  ]

  const process = [
    {
      step: '01',
      title: 'Discovery & Research',
      description: 'We start by understanding your business goals, target audience, and competitive landscape to create a design strategy that works.'
    },
    {
      step: '02',
      title: 'Wireframing & Prototyping',
      description: 'Creating detailed wireframes and interactive prototypes to visualize the user journey and functionality before design.'
    },
    {
      step: '03',
      title: 'Visual Design',
      description: 'Crafting beautiful, on-brand visual designs that capture your brand essence and engage your target audience.'
    },
    {
      step: '04',
      title: 'Testing & Refinement',
      description: 'User testing and iterative improvements to ensure the design meets user needs and business objectives.'
    }
  ]

  const caseStudies = [
    {
      title: 'E-commerce Platform Redesign',
      client: 'Fashion Retailer',
      result: '150% increase in conversion rate',
      description: 'Complete redesign of an e-commerce platform focusing on user experience and conversion optimization.',
      image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=600&h=400&fit=crop'
    },
    {
      title: 'SaaS Dashboard Design',
      client: 'Tech Startup',
      result: '40% reduction in user onboarding time',
      description: 'Intuitive dashboard design that simplified complex data visualization and improved user adoption.',
      image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600&h=400&fit=crop'
    }
  ]

  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 bg-gradient-to-br from-primary-50 via-white to-accent-50">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <div className="inline-flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mb-6">
              <Palette className="w-4 h-4 mr-2" />
              Web Design Services
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-heading font-bold text-secondary-900 mb-6">
              Stunning Web Designs That <span className="gradient-text">Convert</span>
            </h1>
            <p className="text-xl text-secondary-600 mb-8 leading-relaxed">
              We create visually striking and highly functional websites that not only look amazing 
              but also drive business results through strategic design and user experience optimization.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="primary" size="lg">
                Start Your Project
              </Button>
              <Button variant="secondary" size="lg">
                View Portfolio
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-heading font-bold text-secondary-900 mb-6">
                What's Included in Our Web Design Service
              </h2>
              <p className="text-secondary-600 mb-8 leading-relaxed">
                Our comprehensive web design service covers every aspect of creating a successful online presence 
                that engages users and drives conversions.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                    <span className="text-secondary-700">{feature}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="relative">
              <img
                src="https://images.unsplash.com/photo-1559028006-448665bd7c7f?w=600&h=400&fit=crop"
                alt="Web Design Process"
                className="rounded-2xl shadow-2xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="section-padding bg-secondary-50">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-heading font-bold text-secondary-900 mb-6">
              Our Design Process
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              A proven methodology that ensures every project delivers exceptional results
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {process.map((step, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                  {step.step}
                </div>
                <h3 className="text-xl font-heading font-bold text-secondary-900 mb-3">
                  {step.title}
                </h3>
                <p className="text-secondary-600 leading-relaxed">
                  {step.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Case Studies */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-heading font-bold text-secondary-900 mb-6">
              Success Stories
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              See how our web design solutions have transformed businesses
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {caseStudies.map((study, index) => (
              <div key={index} className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-shadow duration-300">
                <img
                  src={study.image}
                  alt={study.title}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <h3 className="text-xl font-heading font-bold text-secondary-900 mb-2">
                    {study.title}
                  </h3>
                  <p className="text-secondary-600 mb-4">{study.description}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-secondary-500">{study.client}</span>
                    <span className="text-green-600 font-semibold">{study.result}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section-padding bg-secondary-900 text-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-heading font-bold mb-6">
              Ready to Transform Your Online Presence?
            </h2>
            <p className="text-xl text-secondary-300 mb-8">
              Let's create a stunning website that drives results for your business.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="primary" size="lg">
                Get Free Consultation
              </Button>
              <Button variant="secondary" size="lg">
                View Pricing
              </Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </main>
  )
}
